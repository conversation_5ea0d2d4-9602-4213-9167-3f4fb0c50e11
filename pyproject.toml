[project]
name = "pmcp"
version = "1.0.0"
description = "Provably Model Context Protocol"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastmcp==2.12.3",
    "loguru==0.7.3",
    "pydantic==2.11.9",
    "pydantic-settings==2.10.1",
    "python-dotenv>=1.1.1",
    "sentry-sdk==2.38",
]

[dependency-groups]
dev = [
    "python-semantic-release==10.4.1",
    "ruff>=0.13.0",
    "sentry-sdk>=2.37.1",
]
[tool.semantic_release]
version_source = "tag"
commit_parser = "conventional"
tag_format = "v{version}"
changelog_file = "CHANGELOG.md"
version_toml = ["pyproject.toml:project.version"]

[tool.semantic_release.branches.main]
match = "(main)"

[tool.semantic_release.changelog]
exclude_commit_patterns = [
    '''chore(?:\([^)]*?\))?: .+''',
    '''ci(?:\([^)]*?\))?: .+''',
    '''refactor(?:\([^)]*?\))?: .+''',
    '''style(?:\([^)]*?\))?: .+''',
    '''test(?:\([^)]*?\))?: .+''',
    '''build\((?!deps\): .+)''',
    '''[Ii]nitial [Cc]ommit.*''',
]
