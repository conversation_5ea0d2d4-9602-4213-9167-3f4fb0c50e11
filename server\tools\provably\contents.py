from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.contents import (
    ContentSearchItem,
    ContentsQueryParams,
    OrganizationContentsResponse,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def list_organization_contents(
    ctx: Context, organization_id: UUID, params: ContentsQueryParams | None = None
) -> OrganizationContentsResponse:
    """List organization contents."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/contents",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True) if params else None,
    )
    return response


list_organization_contents_tool = Tool.from_function(
    fn=list_organization_contents,
    name="provably_list_organization_contents",
    description="List organization contents",
    enabled=True,
)


async def search_organization_contents(
    organization_id: UUID, query: str, ctx: Context
) -> list[ContentSearchItem]:
    """Search organization contents."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/contents/search",
        api_key=api_key,
        params={"query": query},
    )
    return response


search_organization_contents_tool = Tool.from_function(
    fn=search_organization_contents,
    name="provably_search_organization_contents",
    description="Search organization contents",
    enabled=True,
)
