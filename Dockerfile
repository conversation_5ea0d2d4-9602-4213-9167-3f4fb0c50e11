# =============================================================================
# Stage 1: Build Environment - Dependencies and Application Preparation
# =============================================================================
FROM python:3.13-slim AS dependency_builder

# Build-time environment variables for optimization
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    UV_CACHE_DIR=/tmp/uv-cache

# Set build workspace
WORKDIR /build

# Install build dependencies and tools
RUN apt-get update && apt-get install -y \
    # Build tools for compiling Python packages
    build-essential \
    gcc \
    g++ \
    # Utilities for downloading and processing
    curl \
    wget \
    git \
    # Clean up in same layer to reduce image size
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Install UV package manager for fast dependency resolution
RUN pip install --no-cache-dir uv

# Copy dependency specification files
COPY pyproject.toml uv.lock ./

# Create virtual environment and install dependencies
# Using --frozen ensures exact versions from lock file
# --no-dev excludes development dependencies
RUN uv venv /opt/venv \
    && uv sync --frozen --no-dev

# =============================================================================
# Stage 2: Runtime Environment - Minimal Production Image
# =============================================================================
FROM python:3.13-slim AS mcp_server_runtime

# Runtime environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    # Point to the virtual environment from builder stage
    PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/opt/venv/lib/python3.13/site-packages"

# Set application workspace
WORKDIR /opt/pmcp_server

# Install only runtime dependencies (no build tools)
RUN apt-get update && apt-get install -y \
    # Basic utilities for container health checks and operations
    curl \
    # Clean up package cache immediately
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Copy the virtual environment from builder stage
COPY --from=dependency_builder /opt/venv /opt/venv

# Copy application source code
COPY . .

# Create dedicated non-root user for security
# Using descriptive names instead of generic 'pmcp'
RUN groupadd -r mcp_server_group \
    && useradd --no-log-init -r -g mcp_server_group mcp_server_user \
    && chown -R mcp_server_user:mcp_server_group /opt/pmcp_server

# Switch to non-root user
USER mcp_server_user

# Accept port as build argument with sensible default
ARG MCP_SERVER_PORT=8000
ENV MCP_SERVER_PORT=${MCP_SERVER_PORT}

# Expose the configured port
EXPOSE ${MCP_SERVER_PORT}

# Set production environment
ENV APP_ENV=production

# Health check to ensure server is responsive
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${MCP_SERVER_PORT}/health || exit 1

# Run the PMCP Action Server
CMD ["python", "server"]

