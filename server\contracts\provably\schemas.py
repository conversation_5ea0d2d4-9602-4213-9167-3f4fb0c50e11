from uuid import UUID

from pydantic import BaseModel


class SchemaParams(BaseModel):
    name: str


class SchemaBulkCreateParams(BaseModel):
    names: list[str]


class SchemaResponse(BaseModel):
    id: UUID


class MiddlewareInfo(BaseModel):
    name: str
    status: str
    is_self_hosted: bool


class DatabaseInfo(BaseModel):
    name: str
    status: str


class SchemaDetailResponse(BaseModel):
    id: UUID
    name: str
    is_imported: bool
    status: str
    middleware: MiddlewareInfo
    database: DatabaseInfo


class SchemaListResponse(BaseModel):
    id: UUID
    name: str
    status: str


class SchemaBulkDeleteParams(BaseModel):
    ids: list[UUID]
