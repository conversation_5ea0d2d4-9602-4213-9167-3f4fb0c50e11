from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.collections import (
    CollectionDetailResponse,
    CollectionQueryParams,
    CollectionResponse,
    CreateCollectionRequest,
    InviteUsersRequest,
    UpdateCollectionRequest,
)
from server.services.provably_client import (
    OpenProvablyHTTPClientV1,
    ProvablyHTTPClientV1,
)

# Initialize HTTP client
client = OpenProvablyHTTPClientV1()
jwt_client = ProvablyHTTPClientV1()


async def list_collections(
    organization_id: UUID, params: CollectionQueryParams, ctx: Context
) -> list[CollectionResponse]:
    """List collections with optional filtering and pagination."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/collections_frontend",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return [CollectionResponse(**item) for item in response]


list_collections_tool = Tool.from_function(
    fn=list_collections,
    name="provably_list_collections",
    description="List Provably collections with optional filtering and pagination",
    enabled=True,
)


async def create_collection(
    organization_id: UUID, body: CreateCollectionRequest, ctx: Context
) -> dict:
    """Create a new collection."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/collections",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


create_collection_tool = Tool.from_function(
    fn=create_collection,
    name="provably_create_collection",
    description="Create a new collection",
    enabled=True,
)


async def get_collection(
    organization_id: UUID, collection_id: UUID, ctx: Context
) -> CollectionDetailResponse:
    """Get a collection."""
    # api_key = ctx.get_state("api_key")
    jwt_secret = ctx.get_state("jwt_secret")
    response = await jwt_client.get(
        endpoint=f"/organizations/{organization_id}/collections/{collection_id}",
        api_key=jwt_secret,
    )
    return response


get_collection_tool = Tool.from_function(
    fn=get_collection,
    name="provably_get_collection",
    description="Get a collection",
    enabled=True,
)


async def update_collection(
    organization_id: UUID,
    collection_id: UUID,
    multipart: UpdateCollectionRequest,
    ctx: Context,
) -> dict:
    """Update a collection."""
    # api_key = ctx.get_state("api_key")
    jwt_secret = ctx.get_state("jwt_secret")
    files = {"proof_file": (None, "None")}
    await jwt_client.patch_multipart(
        endpoint=f"/organizations/{organization_id}/collections/{collection_id}",
        api_key=jwt_secret,
        data=multipart.model_dump(mode="json", exclude_none=True),
        files=files,
    )
    return {"message": "Collection updated"}


update_collection_tool = Tool.from_function(
    fn=update_collection,
    name="provably_update_collection",
    description="Update a collection",
    enabled=True,
)


async def invite_users_to_collection(
    organization_id: UUID, collection_id: UUID, body: InviteUsersRequest, ctx: Context
) -> dict:
    """Invite users to a collection."""
    # api_key = ctx.get_state("api_key")
    jwt_secret = ctx.get_state("jwt_secret")
    response = await jwt_client.post(
        endpoint=f"/organizations/{organization_id}/collections/{collection_id}/users",
        api_key=jwt_secret,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    print(response)
    return {"message": "Users invited"}


invite_users_to_collection_tool = Tool.from_function(
    fn=invite_users_to_collection,
    name="provably_invite_users_to_collection",
    description="Invite users to a collection",
    enabled=True,
)
