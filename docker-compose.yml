version: '3.8'

networks:
  pmcp-network:
    driver: bridge

services:
  pmcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pmcp-server
    restart: unless-stopped
    env_file:
      - .env
    environment:
      APP_ENV: ${APP_ENV:-prod}
      
      MCP_PORT: ${MCP_PORT:-8000}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      
      PROVABLY_BASE_URL: ${PROVABLY_BASE_URL}
      
      SENTRY_DSN: ${SENTRY_DSN:-}
    ports:
      - "${MCP_PORT:-8000}:${MCP_PORT:-8000}"
    networks:
      - pmcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{MCP_PORT:-8000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


