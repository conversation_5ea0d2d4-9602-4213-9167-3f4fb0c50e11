# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
*.out

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# Documentation
docs/_build/
.readthedocs.yml

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env.local
.env.development
.env.test
.env.production

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# AI code editors
.augment/
.cursor/

# Temporary files
*.tmp
*.temp
.cache/

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Local configuration
config/.env.*
!config/.env.example

# Scripts that shouldn't be in container
scripts/local-*
scripts/dev-*

# README and documentation
README.md
CHANGELOG.md
LICENSE
*.md
