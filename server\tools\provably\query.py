from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.middlewares import (
    ExecuteQueryParams,
    ExecuteQueryResponseData,
    ValidateQueryParams,
    ValidateQueryResponseData,
)
from server.services.provably_client import ProvablyHTTPClientV1

jwt_client = ProvablyHTTPClientV1()


async def execute_sql_query(
    organization_id: UUID,
    middleware_id: UUID,
    body: ExecuteQueryParams,
    ctx: Context,
) -> ExecuteQueryResponseData:
    """Execute a query."""
    jwt_secret = ctx.get_state("jwt_secret")
    response = await jwt_client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/query",
        api_key=jwt_secret,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


execute_sql_query_tool = Tool.from_function(
    fn=execute_sql_query,
    name="provably_execute_sql_query",
    description="Execute an SQL query",
    enabled=True,
)


async def validate_sql_query(
    organization_id: UUID, middleware_id: UUID, body: ValidateQueryParams, ctx: Context
) -> ValidateQueryResponseData:
    """Validate a query."""
    jwt_secret = ctx.get_state("jwt_secret")
    response = await jwt_client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/validate_query",
        api_key=jwt_secret,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


validate_sql_query_tool = Tool.from_function(
    fn=validate_sql_query,
    name="provably_validate_sql_query",
    description="Validate an SQL query",
    enabled=True,
)


async def verify_sql_query(organization_id: UUID, query_id: UUID, ctx: Context) -> dict:
    """Verify a query."""
    jwt_secret = ctx.get_state("jwt_secret")
    response = await jwt_client.post(
        endpoint=f"/organizations/{organization_id}/queries/{query_id}/verify",
        api_key=jwt_secret,
    )
    return {"message": response}


verify_sql_query_tool = Tool.from_function(
    fn=verify_sql_query,
    name="provably_verify_sql_query",
    description="Verify a query",
    enabled=True,
)
