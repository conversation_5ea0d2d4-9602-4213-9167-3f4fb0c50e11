from inspect import cleandoc
from fastmcp.prompts import Prompt


SQL_QUERY_RULES = cleandoc(
    """
* You must use ONLY one of the following aggregations: SUM, COUNT, VARIANCE, STDDEV, MEDIAN
* AVOID using MIN, MAX, and AVG aggregations
* Aggregation can have ONLY one column name inside parenthesis. For example: <PERSON><PERSON>(column_name), <PERSON><PERSON>(column_name)
* Can ONLY use SELECT, FROM and WHERE statements
* The WHERE statement can have following logical operators: <, >, =<, =>, =, IS NULL, IS NOT NULL
* The WHERE statement can ONLY have 1 filtering option
* Avoid using generalized approaches such as COUNT(*)
* Use columns ONLY from the schemas
* Make sure you select correct operation based on question
* Make sure to have exactly one aggregation and one column or analytic function
* ONLY use columns from single collection
"""
)


GENERATE_SQL_QUERY_PROMPT = cleandoc(
    f"""
<task>
You are an expert SQL query generator. Your task is to generate an SQL query based on the given question and execute it using the proper SQL query execution tool. You must validate the query before executing it and use the error messages to improve the query.
You must understand the question, research available collections and organizations to find the right data to answer the question. Base your SQL query on the relevant source table of the collection and its columns.
Make sure you answer the question accurately to every detail. Make sure to follow the rules. Make sure to find the right collection and columns first to answer the question. The query must require a proof generation.
</task>

<rules>
{SQL_QUERY_RULES}
</rules>

<question>
{{question}}
</question>
"""
)


def sql_query_generation(question: str):
    return GENERATE_SQL_QUERY_PROMPT.format(question=question)


sql_query_generation_prompt = Prompt.from_function(
    fn=sql_query_generation,
    name="provably_sql_query_generation_prompt",
    description="Execute an SQL query prompt",
    enabled=True,
)
