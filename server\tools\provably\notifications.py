from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.notifications import (
    NotificationCountResponse,
    NotificationQueryParams,
    NotificationResponse,
    NotificationSettingsResponse,
    UpdateNotificationSettings,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def get_user_notifications(
    params: NotificationQueryParams, ctx: Context
) -> NotificationResponse:
    """Get user notifications."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint="/user/notifications",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return response


get_user_notifications_tool = Tool.from_function(
    fn=get_user_notifications,
    name="provably_get_user_notifications",
    description="Get user notifications",
    enabled=True,
)


async def get_user_notifications_count(ctx: Context) -> NotificationCountResponse:
    """Get user notifications count."""
    api_key = ctx.get_state("api_key")
    response = await client.get(endpoint="/user/notifications/counts", api_key=api_key)
    return response


get_user_notifications_count_tool = Tool.from_function(
    fn=get_user_notifications_count,
    name="provably_get_user_notifications_count",
    description="Get user notifications count",
    enabled=True,
)


async def delete_user_noficiation(notification_id: UUID, ctx: Context) -> dict:
    """Delete user notifications."""
    api_key = ctx.get_state("api_key")
    await client.delete(
        endpoint=f"/user/notifications/{notification_id}", api_key=api_key
    )
    return {"message": "Notification deleted"}


delete_user_noficiation_tool = Tool.from_function(
    fn=delete_user_noficiation,
    name="provably_delete_user_noficiation",
    description="Delete user notifications",
    enabled=True,
)


async def get_notifications_settings(
    ctx: Context,
) -> list[NotificationSettingsResponse]:
    """Get notifications settings."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint="/user/notifications/settings", api_key=api_key
    )
    return response


get_notifications_settings_tool = Tool.from_function(
    fn=get_notifications_settings,
    name="provably_get_notifications_settings",
    description="Get user notifications settings",
    enabled=True,
)


async def update_notifications_settings(
    body: UpdateNotificationSettings, ctx: Context
) -> list[NotificationSettingsResponse]:
    """Update notifications settings."""
    api_key = ctx.get_state("api_key")
    response = await client.put(
        endpoint="/user/notifications/settings",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


update_notifications_settings_tool = Tool.from_function(
    fn=update_notifications_settings,
    name="provably_update_notifications_settings",
    description="Update user notifications settings",
    enabled=True,
)
