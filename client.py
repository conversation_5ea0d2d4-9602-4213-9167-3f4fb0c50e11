import asyncio
from typing import cast

from fastmcp import Client
from fastmcp.client.client import CallToolResult

client = Client("http://127.0.0.1:8000/pmcp", auth="AAA1234567890")

ORGANIZATION_ID = "25a3cf3e-6695-462c-a12d-129bb7874320"
MIDDLEWARE_ID = "bd18a8e2-d828-49e6-8dd2-2cffeae28b87"
BANK_COLLECTIONS_DATABASE_ID = "********-debf-4e69-86ee-c363d70d8514"
# QUERY_ID = "44d57609-a8a7-48dd-bfa6-15d1aa6ba2a3"
QUERY_ID = "b3338994-e920-49d1-9e8e-d678ac1bfa4e"
PUBLIC_BANK_COLLECTIONS_SCHEMA_ID = "ca9e2c0a-5596-4fac-9657-65e9ad568bb7"
COLLECTION_ID = "bff582e7-93a5-4ed1-9727-8458955cfaf7"


async def list_tools():
    async with client:
        tools = await client.list_tools()
        for tool in tools:
            print(f"Tool: {tool.name}")
            print(f"Description: {tool.description}")
            if tool.inputSchema:
                print(f"Parameters: {tool.inputSchema}")
            # Access tags and other metadata
            if hasattr(tool, "meta") and tool.meta:
                fastmcp_meta = tool.meta.get("_fastmcp", {})
                print(f"Tags: {fastmcp_meta.get('tags', [])}")


async def send_hello(name: str):
    async with client:
        result = await client.call_tool("send_hello", {"name": name})
        print(result)


async def list_organizations():
    async with client:
        result = await client.call_tool("provably_list_organizations")
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def list_collections():
    async with client:
        result = await client.call_tool(
            "provably_list_collections",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "params": {"page_size": 5},
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_current_user():
    async with client:
        result = await client.call_tool("provably_get_current_user")
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def search_user():
    async with client:
        result = await client.call_tool(
            "provably_search_user", arguments={"query_params": {"query": "demo"}}
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_user_api_key():
    async with client:
        result = await client.call_tool("provably_get_user_api_key")
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def list_middlewares():
    async with client:
        result = await client.call_tool(
            "provably_list_middlewares",
            arguments={"organization_id": ORGANIZATION_ID},
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def list_databases():
    async with client:
        result = await client.call_tool(
            "provably_list_databases",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def check_database_access():
    async with client:
        result = await client.call_tool(
            "provably_check_database_access",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
                "body": {
                    "name": "bank_collections",
                    "username": "collections_user",
                    "password": "e[Olj`$zLatrg8D",
                    # "password": "e[Olj`$zLatrg8Dn",
                    "provider": "postgresql",
                    "uri": "34.90.246.32:543",
                    # "uri": "34.90.246.32:5432",
                },
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_database():
    async with client:
        result = await client.call_tool(
            "provably_get_database",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
                "database_id": BANK_COLLECTIONS_DATABASE_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def bulk_delete_databases():
    async with client:
        result = await client.call_tool(
            "provably_bulk_delete_databases",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "body": [],
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def add_provably_middleware():
    async with client:
        result = await client.call_tool(
            "provably_add_provably_middleware",
            arguments={
                # "organization_id": ORGANIZATION_ID,
                "organization_id": "989d8ac0-ded4-4e1e-9351-0fa0c27ad171",
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_middleware():
    async with client:
        result = await client.call_tool(
            "provably_get_middleware",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def bulk_delete_middlewares():
    async with client:
        result = await client.call_tool(
            "provably_bulk_delete_middlewares",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "body": [],
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def list_organization_contents():
    async with client:
        result = await client.call_tool(
            "provably_list_organization_contents",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "params": {
                    # "middleware_ids": MIDDLEWARE_ID,
                    # "database_ids": f"{BANK_COLLECTIONS_DATABASE_ID},{STEFAN_AIVEN_DATABASE_ID}",
                    # "schema_ids": "fb82bf88-19e6-4110-99e7-619cd539b7df,2134521e-8eef-4a1a-9577-e12f79f28d74",
                    "table_ids": "0658202c-504c-488a-968a-bb4ba742ed06,88ea6dec-43f8-4652-9e59-c9202cc4c1ff"
                },
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def search_organization_contents():
    async with client:
        result = await client.call_tool(
            "provably_search_organization_contents",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "query": "bank",
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def validate_database():
    async with client:
        result = await client.call_tool(
            "provably_validate_database",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "database_id": BANK_COLLECTIONS_DATABASE_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_query():
    async with client:
        result = await client.call_tool(
            "provably_get_query",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "query_id": QUERY_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def download_proof():
    async with client:
        result = await client.call_tool(
            "provably_download_proof",
            arguments={
                "proof_id": "0833bc30-8381-4627-9c7c-e32a90fc6afe",
            },
        )
        result = cast(CallToolResult, result)
        print(result.content)


async def data_hierarchy():
    async with client:
        result = await client.call_tool(
            "provably_get_data_hierarchy",
            arguments={
                "organization_id": ORGANIZATION_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        # print(result)
        # print("--------")
        print(result.data)


async def create_feedback():
    async with client:
        result = await client.call_tool(
            "provably_create_feedback",
            arguments={
                "description": "This is a test feedback from the MCP server",
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_user_notifications():
    async with client:
        result = await client.call_tool(
            "provably_get_user_notifications",
            arguments={
                "params": {"page": 0, "page_size": 10},
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_organization_logs():
    async with client:
        result = await client.call_tool(
            "provably_get_organization_logs",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "params": {"page": 0, "page_size": 10},
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_organization_notifications():
    async with client:
        result = await client.call_tool(
            "provably_get_organization_notifications",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "params": {"page": 0, "page_size": 10},
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_database_schemas():
    async with client:
        result = await client.call_tool(
            "provably_get_database_schemas",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
                "database_id": BANK_COLLECTIONS_DATABASE_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_database_schema():
    async with client:
        result = await client.call_tool(
            "provably_get_database_schema",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
                "database_id": BANK_COLLECTIONS_DATABASE_ID,
                "schema_id": PUBLIC_BANK_COLLECTIONS_SCHEMA_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_database_tables():
    async with client:
        result = await client.call_tool(
            "provably_get_database_tables",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
                "database_id": BANK_COLLECTIONS_DATABASE_ID,
                "schema_id": PUBLIC_BANK_COLLECTIONS_SCHEMA_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_collection():
    async with client:
        result = await client.call_tool(
            "provably_get_collection",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "collection_id": COLLECTION_ID,
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def update_collection():
    async with client:
        result = await client.call_tool(
            "provably_update_collection",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "collection_id": COLLECTION_ID,
                "multipart": {
                    "name": "Updated collection name",
                    "description": "Updated collection description",
                    "publicity_status": "private",
                },
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def invite_users_to_collection():
    async with client:
        result = await client.call_tool(
            "provably_invite_users_to_collection",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "collection_id": COLLECTION_ID,
                "body": {
                    "users": [
                        {
                            "email": "<EMAIL>",
                        },
                    ],
                },
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def validate_sql_query():
    async with client:
        result = await client.call_tool(
            "provably_validate_sql_query",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
                "body": {
                    "query": "SELECT COUNT(age) FROM passenger",
                    "collection_id": COLLECTION_ID,
                },
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def execute_sql_query():
    async with client:
        result = await client.call_tool(
            "provably_execute_sql_query",
            arguments={
                "organization_id": ORGANIZATION_ID,
                "middleware_id": MIDDLEWARE_ID,
                "body": {
                    "query": "SELECT COUNT(passengerid) FROM passengers",
                    "require_proof": False,
                    "collection_id": COLLECTION_ID,
                },
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def main():
    await send_hello("John")
    # await list_organizations()
    # await list_collections()
    # await get_current_user()
    # await search_user()
    # await get_user_api_key()
    # await list_middlewares()
    # await list_databases()
    # await check_database_access()
    # await get_database()
    # await bulk_delete_databases()
    # await add_provably_middleware()
    # await get_middleware()
    # await bulk_delete_middlewares()
    # await list_organization_contents()
    # await search_organization_contents()
    # await validate_database()
    # await get_query()
    # await download_proof()
    # await data_hierarchy()
    # await create_feedback()
    # await get_user_notifications()
    # await get_organization_logs()
    # await get_organization_notifications()
    # await get_database_schemas()
    # await get_database_schema()
    # await get_database_tables()
    # await get_collection()
    # await update_collection()
    # await invite_users_to_collection()
    # await validate_sql_query()
    # await execute_sql_query()


asyncio.run(main())
