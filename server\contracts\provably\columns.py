from enum import Enum
from uuid import UUID

from pydantic import BaseModel, Field


class ColumnType(str, Enum):
    INTEGER = "integer"
    BIGINT = "bigint"
    SMALLINT = "smallint"
    DECIMAL = "decimal"
    TIME = "time"
    TIMESTAMP = "timestamp"
    DATE = "date"
    BOOLEAN = "boolean"
    UUID = "uuid"
    STRING = "string"


class ColumnCreateParam(BaseModel):
    name: str
    type: ColumnType


class ColumnBulkCreateParams(BaseModel):
    columns: list[ColumnCreateParam]


class ColumnResponse(BaseModel):
    id: UUID
    name: str
    column_type: ColumnType
    is_imported: bool
    status: str


class MiddlewareInfo(BaseModel):
    name: str
    status: str
    is_self_hosted: bool


class DatabaseInfo(BaseModel):
    name: str
    status: str


class SchemaInfo(BaseModel):
    name: str
    status: str
    is_imported: bool


class TableInfo(BaseModel):
    name: str
    status: str
    is_imported: bool


class ColumnDetailResponse(BaseModel):
    id: UUID
    name: str
    type: ColumnType
    is_imported: bool
    status: str
    middleware: MiddlewareInfo
    database: DatabaseInfo
    schema_ref: SchemaInfo = Field(..., alias="schema")
    table: TableInfo


class ColumnParams(BaseModel):
    name: str
    type: ColumnType


class ColumnBulkDeleteParams(BaseModel):
    ids: list[UUID]
