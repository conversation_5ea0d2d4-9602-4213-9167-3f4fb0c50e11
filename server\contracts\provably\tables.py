from uuid import UUID

from pydantic import BaseModel, Field


class TableListItem(BaseModel):
    id: UUID
    name: str
    status: str


class MiddlewareInfo(BaseModel):
    name: str
    status: str
    is_self_hosted: bool


class DatabaseInfo(BaseModel):
    name: str
    status: str


class SchemaInfo(BaseModel):
    name: str
    status: str
    is_imported: bool


class TableDetailResponse(BaseModel):
    id: UUID
    name: str
    is_imported: bool
    status: str
    middleware: MiddlewareInfo
    database: DatabaseInfo
    schema_ref: SchemaInfo = Field(..., alias="schema")


class TableParams(BaseModel):
    name: str


class TableResponse(BaseModel):
    id: UUID


class TableBulkDeleteParams(BaseModel):
    ids: list[UUID]
