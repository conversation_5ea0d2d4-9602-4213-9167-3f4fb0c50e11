from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool


from server.contracts.provably.columns import (
    ColumnBulkCreateParams,
    ColumnBulkDeleteParams,
    ColumnDetailResponse,
    ColumnParams,
    ColumnResponse,
)
from server.services.provably_client import (
    OpenProvablyHTTPClientV1,
    ProvablyHTTPClientV1,
)

# Initialize HTTP client
client = OpenProvablyHTTPClientV1()
jwt_client = ProvablyHTTPClientV1()


async def bulk_create_columns(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    table_id: UUID,
    body: ColumnBulkCreateParams,
    ctx: Context,
) -> list[ColumnResponse]:
    """Bulk create columns."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables/{table_id}/columns/bulk_create",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


bulk_create_columns_tool = Tool.from_function(
    fn=bulk_create_columns,
    name="provably_bulk_create_columns",
    description="Bulk create columns",
    enabled=True,
)


async def list_table_columns(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    table_id: UUID,
    ctx: Context,
) -> list[ColumnResponse]:
    """List all columns."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables/{table_id}/columns",
        api_key=api_key,
    )
    return response


list_table_columns_tool = Tool.from_function(
    fn=list_table_columns,
    name="provably_list_table_columns",
    description="List all table columns",
    enabled=True,
)


async def get_table_column(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    table_id: UUID,
    column_id: UUID,
    ctx: Context,
) -> ColumnDetailResponse:
    """Get a column."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables/{table_id}/columns/{column_id}",
        api_key=api_key,
    )
    return response


get_table_column_tool = Tool.from_function(
    fn=get_table_column,
    name="provably_get_table_column",
    description="Get a table column",
    enabled=True,
)


async def update_table_column(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    table_id: UUID,
    column_id: UUID,
    body: ColumnParams,
    ctx: Context,
) -> ColumnResponse:
    """Update a column."""
    api_key = ctx.get_state("api_key")
    response = await client.put(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables/{table_id}/columns/{column_id}",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


update_table_column_tool = Tool.from_function(
    fn=update_table_column,
    name="provably_update_table_column",
    description="Update a table column",
    enabled=True,
)


async def delete_table_column(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    table_id: UUID,
    column_id: UUID,
    ctx: Context,
) -> dict:
    """Delete a column."""
    api_key = ctx.get_state("api_key")
    await client.delete(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables/{table_id}/columns/{column_id}",
        api_key=api_key,
    )
    return {"message": "Column deleted"}


delete_table_column_tool = Tool.from_function(
    fn=delete_table_column,
    name="provably_delete_table_column",
    description="Delete a table column",
    enabled=True,
)


async def bulk_delete_table_columns(
    organization_id: UUID,
    body: ColumnBulkDeleteParams,
    ctx: Context,
) -> dict:
    """Bulk delete table columns."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/columns/bulk_delete",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return {"message": "Columns deleted"}


bulk_delete_table_columns_tool = Tool.from_function(
    fn=bulk_delete_table_columns,
    name="provably_bulk_delete_table_columns",
    description="Bulk delete table columns",
    enabled=True,
)
