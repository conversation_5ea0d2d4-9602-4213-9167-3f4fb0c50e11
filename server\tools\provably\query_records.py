from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.query_records import QueryRecordResponse
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def get_query(
    organization_id: UUID, query_id: UUID, ctx: Context
) -> QueryRecordResponse:
    """Get a query."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/queries/{query_id}",
        api_key=api_key,
    )
    return response


get_query_tool = Tool.from_function(
    fn=get_query,
    name="provably_get_query",
    description="Get a query",
    enabled=True,
)
