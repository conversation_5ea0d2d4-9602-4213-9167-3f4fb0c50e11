from uuid import UUI<PERSON>

from fastmcp import Context
from fastmcp.tools import Tool

from server.services.provably_client import (
    OpenProvablyHTTPClientV1,
    ProvablyHTTPClientV1,
)

jwt_client = ProvablyHTTPClientV1()
client = OpenProvablyHTTPClientV1()


async def download_proof(proof_id: UUID, ctx: Context) -> str:
    """Download a proof."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/proof_requests/{proof_id}/download",
        api_key=api_key,
    )
    return response


download_proof_tool = Tool.from_function(
    fn=download_proof,
    name="provably_download_proof",
    description="Download a proof",
    enabled=True,
)


# TODO: solve the issue with the backend first and then imlement the tool
# async def verify_external_proof(
#     organization_id: UUID,
#     proof_data: str,
#     ctx: Context,
#     filename: str = "proof.provably"
# ) -> Any:
#     """Verify an external proof by uploading proof data as multipart form data.

#     Args:
#         organization_id: The organization ID
#         proof_data: The proof data as a string of comma-separated numbers (e.g., "99,105,116,121,0,0,0,0,0,48,0,0,0,0")
#         ctx: FastMCP context
#         filename: Optional filename for the uploaded proof file
#     """
#     api_key = ctx.get_state("api_key")

#     # Prepare the proof data as a file for multipart upload
#     files = {
#         "proof_file": (filename, proof_data.encode('utf-8'), "text/plain")
#     }

#     response = await client.post_multipart(
#         endpoint=f"/organizations/{organization_id}/queries/verify_proof",
#         api_key=api_key,
#         files=files,
#     )
#     return response

# verify_external_proof_tool = Tool.from_function(
#     fn=verify_external_proof,
#     name="provably_verify_external_proof",
#     description="Verify an external proof by uploading proof data as multipart form data. The proof_data should be a string of comma-separated numbers (e.g., '99,105,116,121,0,0,0,0,0,48,0,0,0,0').",
#     enabled=True,
# )
