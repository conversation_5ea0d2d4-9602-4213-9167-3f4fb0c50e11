from enum import Enum
from uuid import UUID

from pydantic import BaseModel, Field


class MiddlewareResponse(BaseModel):
    id: UUID
    name: str | None = None
    url_address: str | None = None
    status: str
    is_self_hosted: bool


class UpdateMiddlewareParams(BaseModel):
    name: str | None = None
    url: str | None = None
    api_key: str | None = None


class ExecuteQueryParams(BaseModel):
    query: str
    require_proof: bool = Field(
        True,
        description="Whether to require proof bytes for the query. True by default.",
    )
    collection_id: UUID


class QueryColumnInfo(BaseModel):
    name: str
    type: str  # One of the types supported by ColumnType


class QueryResultType(str, Enum):
    """Result type for query execution

    - AGGREGATE: Single value result (aggregations, counts, etc)
    - RESULTSET: Tabular data with multiple rows/columns
    """

    AGGREGATE = "aggregate"
    RESULTSET = "resultset"


class TabularData(BaseModel):
    columns: list[QueryColumnInfo]
    rows: list[list]


class QueryAnswer(BaseModel):
    type: QueryResultType
    value: str | TabularData


class ExecuteQueryResponseData(BaseModel):
    query_id: str
    answer: QueryAnswer


class ValidateQueryParams(BaseModel):
    query: str
    collection_id: UUID


class ValidateQueryResponseData(BaseModel):
    query_valid: bool
    message: str | None = None
