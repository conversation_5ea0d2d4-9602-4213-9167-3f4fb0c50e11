from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.organization_access import (
    CollectionAccessRequestQueryParams,
    CollectionAccessRequestResponse,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def accept_organization_invite(organization_id: UUID, ctx: Context) -> dict:
    """Accept an organization invite."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/access/accept_invite",
        api_key=api_key,
    )
    return {"message": "Invite accepted"}


accept_organization_invite_tool = Tool.from_function(
    fn=accept_organization_invite,
    name="provably_accept_organization_invite",
    description="Accept an organization invite",
    enabled=True,
)


async def decline_organization_invite(organization_id: UUID, ctx: Context) -> dict:
    """Decline an organization invite."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/access/decline_invite",
        api_key=api_key,
    )
    return {"message": "Invite declined"}


decline_organization_invite_tool = Tool.from_function(
    fn=decline_organization_invite,
    name="provably_decline_organization_invite",
    description="Decline an organization invite",
    enabled=True,
)


async def accept_collection_access_request(
    organization_id: UUID, collection_id: UUID, ctx: Context
) -> dict:
    """Accept a collection access request."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/collections/{collection_id}/access/accept_invite",
        api_key=api_key,
    )
    return {"message": "Access request accepted"}


accept_collection_access_request_tool = Tool.from_function(
    fn=accept_collection_access_request,
    name="provably_accept_collection_access_request",
    description="Accept a collection access request",
    enabled=True,
)


async def decline_collection_access_request(
    organization_id: UUID, collection_id: UUID, ctx: Context
) -> dict:
    """Decline a collection access request."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/collections/{collection_id}/access/decline_invite",
        api_key=api_key,
    )
    return {"message": "Access request declined"}


decline_collection_access_request_tool = Tool.from_function(
    fn=decline_collection_access_request,
    name="provably_decline_collection_access_request",
    description="Decline a collection access request",
    enabled=True,
)


async def list_collection_access_requests(
    organization_id: UUID, params: CollectionAccessRequestQueryParams, ctx: Context
) -> list[CollectionAccessRequestResponse]:
    """List collection access requests."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/collections/access/requests",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return response


list_collection_access_requests_tool = Tool.from_function(
    fn=list_collection_access_requests,
    name="provably_list_collection_access_requests",
    description="List collection access requests",
    enabled=True,
)


async def approve_collection_access_request(
    organization_id: UUID, request_id: UUID, ctx: Context
) -> dict:
    """Approve a collection access request."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/collections/access/requests/{request_id}/approve",
        api_key=api_key,
    )
    return {"message": "Access request approved"}


approve_collection_access_request_tool = Tool.from_function(
    fn=approve_collection_access_request,
    name="provably_approve_collection_access_request",
    description="Approve a collection access request",
    enabled=True,
)


async def deny_collection_access_request(
    organization_id: UUID, request_id: UUID, ctx: Context
) -> dict:
    """Deny a collection access request."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/collections/access/requests/{request_id}/deny",
        api_key=api_key,
    )
    return {"message": "Access request denied"}


deny_collection_access_request_tool = Tool.from_function(
    fn=deny_collection_access_request,
    name="provably_deny_collection_access_request",
    description="Deny a collection access request",
    enabled=True,
)
