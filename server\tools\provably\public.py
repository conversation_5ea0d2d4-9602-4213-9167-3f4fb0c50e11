from fastmcp import Context
from fastmcp.tools import Tool

from server.services.provably_client import ProvablyHTTPClientV1

client = ProvablyHTTPClientV1()


async def subscribe_to_newsletter(email: str, ctx: Context) -> dict:
    """Subscribe to newsletter."""
    await client.post(
        endpoint="/public/subscribe",
        data={"email": email},
    )
    return {"message": "Subscribed to newsletter"}


subscribe_to_newsletter_tool = Tool.from_function(
    fn=subscribe_to_newsletter,
    name="provably_subscribe_to_newsletter",
    description="Subscribe to Provably newsletter",
    enabled=True,
)
